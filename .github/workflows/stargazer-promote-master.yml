name: "[<PERSON>ga<PERSON>] Promote master"

on:
  workflow_dispatch:
  schedule:
    - cron: "2 5 * * 3"

concurrency:
  group: "stargazer-promote-master"
  cancel-in-progress: false

jobs:
  blocker-check:
    runs-on:
      - common-runner-v2
    steps:
      - name: Blocker check
        uses: anduintransaction/gondolin/actions/jira-blocker-check@master
        with:
          token: ${{ secrets.JIRA_BLOCKER_TOKEN }}
          filter_id: "10145"

      - name: Announce failure
        uses: anduintransaction/gondolin/actions/simple-slack-notification@master
        if: ${{ failure() }}
        with:
          webhook-url: ${{ secrets.SLACK_WEBHOOK }}
          channel: release-squad
          color: danger
          title: "Jira blocker issue"
          message: "<!here> skip stargazer branch promotion due to blocker issue exists. Link: <https://anduin.atlassian.net/issues/?filter=10145>"

  promote:
    needs: ["blocker-check"]
    runs-on:
      - common-runner-v2
    environment: "gondor-internal"
    timeout-minutes: 180
    steps:
      - name: Generate app token
        id: generate_token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ vars.GIMLI_APP_ID }}
          private-key: ${{ secrets.GIMLIBOT_CERT }}
          owner: anduintransaction
          repositories: "stargazer"
          skip-token-revoke: true # should already be expired

      - name: Checkout
        uses: actions/checkout@v4
        with:
          repository: anduintransaction/stargazer
          ref: master
          token: "${{ steps.generate_token.outputs.token }}"

      - name: Promote
        uses: anduintransaction/gondolin/actions/stargazer-promote@master
        with:
          stargazer-dir: .
          branch: master
          slack-webhook: "${{ secrets.SLACK_WEBHOOK }}"
          ecr-repository: "${{ vars.STARGAZER_IMAGE }}"

  upgrade:
    uses: ./.github/workflows/stargazer-full-upgrade.yml
    needs: ["promote"]
    with:
      environment: gondor-internal
    secrets: inherit

  announce:
    needs: ["upgrade"]
    runs-on:
      - common-runner-v2
    environment: "gondor-internal"
    container:
      image: public.ecr.aws/anduintransact/deployer:*******
      options: --user 1001
      volumes:
        - /var/run/secrets/kubernetes.io/serviceaccount:/var/run/secrets/kubernetes.io/serviceaccount
      env:
        KUBERNETES_SERVICE_HOST: kubernetes.default.svc
        KUBERNETES_SERVICE_PORT: "443"
    steps:
      - name: Materialize kube config
        run: |
          mkdir -p ~/.kube
          echo "$ANDUIN_KUBE_CONFIG" | base64 -d > ~/.kube/config
        env:
          ANDUIN_KUBE_CONFIG: "${{ secrets.ANDUIN_KUBE_CONFIG }}"

      - name: Debug kubeconfig
        run: |
          kubectl config view

      - name: Extract image tag
        id: image-extract
        run: |
          imageTag=$(kubectl --context ${{ vars.RIVENDELL_CONTEXT }} \
            -n gondor-internal \
            get deployments gondor -o json \
            | jq -r '.spec.template.spec.containers[0].image' \
            | cut -d':' -f2)
          echo "IMAGE_TAG=${imageTag}" >> "$GITHUB_OUTPUT"

      - name: Announce
        uses: anduintransaction/gondolin/actions/simple-slack-notification@master
        with:
          webhook-url: ${{ secrets.SLACK_WEBHOOK }}
          channel: release-squad
          title: "Stargazer release announcement"
          message: "<!here> Image \\`${{ steps.image-extract.outputs.IMAGE_TAG }}\\` was released into \\`internal\\`"

      - name: Announce delayed cut
        if: github.event_name == 'workflow_dispatch'
        uses: anduintransaction/gondolin/actions/simple-slack-notification@master
        with:
          webhook-url: ${{ secrets.SLACK_WEBHOOK }}
          channel: vn-engineers
          title: "[Importance update] - Stargazer delayed release announcement"
          message: "<!here> Image \\`${{ steps.image-extract.outputs.IMAGE_TAG }}\\` was released into \\`internal\\`. This is a delayed release from the normal schedule."
