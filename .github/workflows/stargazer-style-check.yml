name: "[CI] Style check"

on:
  workflow_call:

jobs:
  run:
    runs-on:
      - common-runner-v2
    container:
      image: public.ecr.aws/anduintransact/anduin-runner-build:1.11.0-jammy
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          set-safe-directory: "${{ env.GITHUB_WORKSPACE }}"

      - name: Style check
        run: ./ci/scripts/build/style-check.sh
