name: "[<PERSON><PERSON>] - Scaler out"

on:
  workflow_call:
    inputs:
      node_group_name:
        required: true
        type: string
      desired_capacity:
        required: true
        type: string

jobs:
  run:
    timeout-minutes: 240
    runs-on:
      - scaler

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          set-safe-directory: "${{ env.GITHUB_WORKSPACE }}"
          fetch-depth: 0

      - name: Setup AWS CLI
        uses: anduintransaction/gondolin/actions/setup-aws-cli@master

      - uses: azure/setup-kubectl@v4
        with:
          version: "latest"

      - name: Scaling out
        id: scaling
        run: |
          ./scripts/cicd-scaler.sh out
        env:
          NODE_GROUP_NAME: "${{ inputs.node_group_name }}"
          SCALE_OUT_DESIRED_CAPACITY: "${{ inputs.desired_capacity }}"

      - name: Slack notification
        uses: anduintransaction/gondolin/actions/simple-slack-notification@master
        with:
          webhook-url: ${{ secrets.SLACK_WEBHOOK }}
          channel: system-stuffs
          title: "[CI-CD] - Scaling out nodeGroup ${{ inputs.node_group_name }}"
          message: "Successfully !!!\n${{ steps.scaling.outputs.MESSAGE }}"
