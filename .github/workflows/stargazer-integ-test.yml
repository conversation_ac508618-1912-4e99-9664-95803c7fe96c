name: "[CI] Integ test"

on:
  workflow_call:

jobs:
  run:
    runs-on:
      - integ-runner-v2
    container:
      image: public.ecr.aws/anduintransact/anduin-runner-build:1.11.0-jammy
      volumes:
        - /var/run/secrets/kubernetes.io/serviceaccount:/var/run/secrets/kubernetes.io/serviceaccount
        - /run/docker:/run/docker
      env:
        DOCKER_HOST: unix:///run/docker/docker.sock
        KUBERNETES_SERVICE_HOST: kubernetes.default.svc
        KUBERNETES_SERVICE_PORT: "443"
        DEPLOYMENT_POD_NAME: "${{ runner.name }}"
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          set-safe-directory: "${{ env.GITHUB_WORKSPACE }}"
          fetch-depth: 0

      - name: Export namespace
        run: |
          export NAMESPACE_PREFIX="spr-${{ github.event.pull_request.number }}"
          echo "NAMESPACE_PREFIX=$NAMESPACE_PREFIX" >> $GITHUB_ENV
          echo "NAMESPACE=$NAMESPACE_PREFIX-$(date +%s)" >> $GITHUB_ENV

      - name: Cleanup environment
        continue-on-error: true
        run: |
          ./ci/scripts/build/runner-integ-clean.sh prefix

      - name: Login Docker
        run: |
          aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin 988983267146.dkr.ecr.us-east-1.amazonaws.com

      - name: Pull cache
        run: |
          ./ci/scripts/build/runner-copy-cache.sh pull
        env:
          BRANCH_NAME: ${{ github.base_ref || github.ref_name }}

      - name: Pull artifact
        run: |
          ./ci/scripts/build/runner-copy-artifact.sh pull runid-${{ github.run_id }}
        env:
          BRANCH_NAME: ${{ github.base_ref || github.ref_name }}

      - name: Check if rerun is needed
        if: success() || failure()
        id: check
        run: |
          ./ci/scripts/build/runner-integ-check.sh
        env:
          ANDUIN_BUILD_ENV: pr

      - name: Prepare environment
        if: ${{ steps.check.outputs.INTEG_TEST_RERUN > 0 }}
        timeout-minutes: 30
        run: | 
          ./ci/scripts/build/runner-integ-env.sh
        env:
          GH_NPM_READ_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Debug error
        continue-on-error: true
        if: always()
        run: |
          echo "All pods"
          kubectl -n $NAMESPACE get pods

          echo "Debug failed pods"
          kubectl -n $NAMESPACE get pods | \
            grep -Ev " Running | Completed |^NAME" | \
            awk '{print $1}' | \
            xargs -I {} sh -c "kubectl -n $NAMESPACE describe pod {}; kubectl -n $NAMESPACE logs {}"

      - name: JDK info
        run: |
          java -version

      - name: Integ test
        run: |
          ./ci/scripts/build/runner-integ-test.sh
        env:
          ANDUIN_BUILD_ENV: pr
          STARGAZER_TIMEOUT: "5 minute"
          INTEG_TEST_RERUN: ${{ steps.check.outputs.INTEG_TEST_RERUN }}
        timeout-minutes: 120

      - name: Report
        uses: mikepenz/action-junit-report@v5
        if: success() || failure()
        with:
          check_name: civ3/integ-test
          report_paths: ./out/**/integration-test-report.xml

      - name: Report S3
        uses: anduintransaction/gondolin/actions/junit-analytics@master
        if: success() || failure()
        continue-on-error: true
        with:
          report_paths: ./out/**/integration-test-report.xml
          s3_bucket: stargazer-github-actions
          s3_dir: ci/raw
          json_fn: runid-${{ github.run_id }}-integ-test.json
          pr_num: "${{ github.event.pull_request.number }}"

      - name: Cleanup environment
        if: always()
        run: |
          ./ci/scripts/build/runner-integ-clean.sh namespace
