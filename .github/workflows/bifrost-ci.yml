name: "[CI] Build (PR)"

on:
  workflow_call:

jobs:
  build-test:
    runs-on:
      - heavy-runner-v2
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          set-safe-directory: "${{ env.GITHUB_WORKSPACE }}"
          fetch-depth: 0

      - name: Setup java
        uses: actions/setup-java@v3
        with:
          distribution: "temurin"
          java-version: "17"

      - name: Setup docker compose v2
        uses: ndeloof/install-compose-action@v0.0.1

      - name: Build
        run: |
          ./ci/scripts/build/build-ci.sh

      - name: Pre Integ-test
        run: |
          ./ci/scripts/init/integ-test/init.sh

      - name: Integ-test
        run: |
          ./ci/scripts/build/integ-test.sh

      - name: Post Integ-test
        run: |
          ./ci/scripts/init/integ-test/finalize.sh
