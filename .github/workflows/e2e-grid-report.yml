name: "[Selenium Grid] Anduin E2E test"

on:
  workflow_call:
    inputs:
      branch:
        required: true
        type: string
      server:
        required: true
        type: string
      build_name:
        type: string
        required: true

jobs:
  report:

    runs-on:
      - e2e-runner

    container:
      image: public.ecr.aws/anduintransact/anduin-runner-build:1.11.0-jammy

    steps:
      - name: Checkout master
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up JDK 11
        uses: actions/setup-java@v4
        with:
          java-version: '11'
          distribution: 'temurin'

      - name: Checkout test branch
        run: |
          ./ci/scripts/build/checkout.sh
        env:
          INPUT_BRANCH: ${{ inputs.branch }}

      - name: Generate report and set report summary
        if: success() || failure()
        id: report_info
        continue-on-error: true
        run: |
          ./ci/scripts/build/generate-report.grid.sh generateReport
        env:
          INPUT_SERVER: ${{ inputs.server }}
          INPUT_BUILD_NAME: ${{ inputs.build_name }}
          WORKFLOW_RUN_ID: ${{ github.run_id }}
          R2_CLOUDFLARE_KEY: ${{ secrets.R2_CLOUDFLARE_KEY }}
          R2_CLOUDFLARE_SECRET: ${{ secrets.R2_CLOUDFLARE_SECRET }}
          R2_CLOUDFLARE_URL: ${{ secrets.R2_CLOUDFLARE_URL }}

      - name: Slack notification
        if: success() || failure()
        uses: rtCamp/action-slack-notify@v2
        continue-on-error: true
        env:
          SLACK_WEBHOOK: "${{ secrets.SLACK_WEBHOOK }}"
          SLACK_ICON: "https://avatars.githubusercontent.com/u/44036562"
          SLACK_USERNAME: Actions
          SLACK_CHANNEL: e2e-test-report
          SLACK_MESSAGE: "${{ inputs.build_name }} report: <${{ steps.report_info.outputs.REPORT_URL }}|Open>\n
          ${{ steps.report_info.outputs.REPORT_SUMMARY }}"
          SLACK_FOOTER: "Powered by Github Actions"
          MSG_MINIMAL: true
