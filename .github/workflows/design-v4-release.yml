name: "Release"

on:
  workflow_call:

jobs:
  run:
    runs-on:
      - common-runner-v2
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          set-safe-directory: "${{ env.GITHUB_WORKSPACE }}"
          fetch-depth: 0

      - uses: actions/setup-java@v4
        with:
          distribution: "temurin"
          java-version: "21"

      - uses: actions/setup-node@v4
        with:
          node-version: 22

      - name: install yarn
        run: |
          corepack enable

      - uses: anduintransaction/gondolin/actions/setup-aws-cli@master

      - name: Acquire git tag
        id: gitvers
        run: |
          echo "APP_VERSION=$(git describe --tags --exact-match 2> /dev/null || git describe --tags --abbrev=40 | rev | cut -c 35-40,42- | rev)" >> $GITHUB_OUTPUT

      - name: Cache Mill output
        uses: actions/cache@v4
        with:
          path: |
             out
             ~/.mill
          key: ${{ runner.os }}-mill-${{ hashFiles('build.mill') }}-${{ hashFiles('**/*.scala') }}
          restore-keys: |
            ${{ runner.os }}-mill-

      - name: Build
        run: |
          yarn install
          yarn build

      - name: Publish
        run: |
          ./ci/scripts/build/runner-publish-artifact.sh
        env:
          ARTIFACTORY_USER: build
          ARTIFACTORY_PW: ${{ secrets.ARTIFACTORY_PW }}

      - name: Setup Pages
        id: pages
        uses: actions/configure-pages@v5

      - name: Upload artifact
        uses: actions/upload-pages-artifact@v3
        with:
          path: ./doc/dist

      - name: Deploy to GitHub Pages
        id: deployment
        uses: actions/deploy-pages@v4

      - name: Slack notification
        uses: rtCamp/action-slack-notify@v2
        continue-on-error: true
        env:
          SLACK_WEBHOOK: "${{ secrets.SLACK_WEBHOOK }}"
          SLACK_ICON: "https://avatars.githubusercontent.com/u/44036562"
          SLACK_USERNAME: Actions
          SLACK_CHANNEL: system-stuffs
          SLACK_MESSAGE: "Version: `${{ steps.gitvers.outputs.APP_VERSION }}`"
          SLACK_COLOR: "${{ job.status }}"
          SLACK_FOOTER: "Powered by Github Actions"
          SITE_TITLE: Build status
          SITE_NAME: "${{ job.status }}"
          HOST_TITLE: Repo
          HOST_NAME: acl4
