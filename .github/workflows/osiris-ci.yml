name: "[CI] Build (PR)"

on:
  workflow_call:

jobs:
  run:
    runs-on:
      - common-runner-v2
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          set-safe-directory: "${{ env.GITHUB_WORKSPACE }}"
          fetch-depth: 0

      - uses: actions/setup-java@v4
        with:
          distribution: "temurin"
          java-version: "21"

      - uses: actions/setup-node@v4
        with:
          node-version: 21

      - uses: anduintransaction/gondolin/actions/setup-aws-cli@master

      - name: Build
        run: |
          corepack enable
          ./ci/scripts/build/build.sh
