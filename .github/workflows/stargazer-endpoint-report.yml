name: "[CI] Endpoint Report (PR)"

on:
  workflow_call:

jobs:
  run:
    runs-on:
      - integ-runner-v2
    container:
      image: public.ecr.aws/anduintransact/anduin-runner-build:1.11.0-jammy
      volumes:
        - /var/run/secrets/kubernetes.io/serviceaccount:/var/run/secrets/kubernetes.io/serviceaccount
        - /run/docker:/run/docker
      env:
        DOCKER_HOST: unix:///run/docker/docker.sock
        KUBERNETES_SERVICE_HOST: kubernetes.default.svc
        KUBERNETES_SERVICE_PORT: "443"
    steps:
      - name: Checkout head
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          set-safe-directory: "${{ env.GITHUB_WORKSPACE }}"
          fetch-depth: 0

      - name: Export namespace
        run: |
          export NAMESPACE_PREFIX="sendpoint-${{ github.event.pull_request.number }}"
          echo "NAMESPACE_PREFIX=$NAMESPACE_PREFIX" >> $GITHUB_ENV
          echo "NAMESPACE=$NAMESPACE_PREFIX-$(date +%s)" >> $GITHUB_ENV

      - name: Cleanup environment
        continue-on-error: true
        run: |
          ./ci/scripts/build/runner-integ-clean.sh prefix

      - name: Login Docker
        run: |
          aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin ************.dkr.ecr.us-east-1.amazonaws.com

      - name: Prepare environment
        timeout-minutes: 30
        run: |
          ./ci/scripts/build/runner-integ-env.sh
        env:
          GH_NPM_READ_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Pull cache
        if: ${{ ! contains(github.event.pull_request.labels.*.name, 'Cache/None') }}
        run: |
          ./ci/scripts/build/runner-copy-cache.sh pull
        env:
          BRANCH_NAME: ${{ github.base_ref || github.ref_name }}

      - name: Pull artifact
        if: ${{ ! contains(github.event.pull_request.labels.*.name, 'Cache/None') }}
        run: |
          ./ci/scripts/build/runner-copy-artifact.sh pull --remote-cache
        env:
          BRANCH_NAME: ${{ github.base_ref || github.ref_name }}

      - name: Create local folder
        run: |
          mkdir local

      - name: Generate head endpoint report
        env:
          STARGAZER_ENDPOINT_REPORT_FILE: local/head-endpoint-report.json
        run: |
          ./ci/scripts/build/runner-endpoint-report.sh

      - name: Checkout base
        run: |
          /usr/bin/git checkout -f ${{ github.event.pull_request.base.sha }}

      - name: Generate base endpoint report
        env:
          STARGAZER_ENDPOINT_REPORT_FILE: local/base-endpoint-report.json
        run: |
          ./ci/scripts/build/runner-endpoint-report.sh

      - name: Compare endpoint reports
        continue-on-error: true
        run: |
          diff --color -u local/base-endpoint-report.json local/head-endpoint-report.json

      - name: Cleanup environment
        if: always()
        run: |
          ./ci/scripts/build/runner-integ-clean.sh namespace
        env:
          ANDUIN_BUILD_ENV: pr

