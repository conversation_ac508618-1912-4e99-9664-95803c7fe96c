name: "Nuke simulator-test"

permissions:
  contents: read
  packages: read

on:
  schedule:
    - cron: "00 2 * * 4"

jobs:

  blocker-check:
    runs-on:
      - common-runner-v2
    steps:
      - name: Blocker check
        uses: anduintransaction/gondolin/actions/jira-blocker-check@master
        with:
          token: ${{ secrets.JIRA_BLOCKER_TOKEN }}
          filter_id: "10214"

      - name: Announce failure
        uses: anduintransaction/gondolin/actions/simple-slack-notification@master
        if:  ${{ failure() }}
        with:
          webhook-url: ${{ secrets.SLACK_WEBHOOK }}
          channel: release-squad
          color: danger
          title: "Jira blocker issue"
          message: "<!here> skip stargazer simulator-test deployment due to blocker issue exists. Link: <https://anduin.atlassian.net/issues/?filter=10214>"

  upgrade:
    needs: ["blocker-check"]
    uses: ./.github/workflows/simulator-nuke.yml
    with:
      environment: "simulator-test"
    secrets: inherit
