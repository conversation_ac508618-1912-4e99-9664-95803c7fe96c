name: "[Server<PERSON>] Deploy Typescript"

on:
  workflow_call:
    inputs:
      serverlessFunction:
        required: true
        type: string
      region:
        required: true
        type: string
      ref:
        required: true
        type: string
      functionDescription:
        required: false
        type: string

jobs:
  build-deploy:
    runs-on: deployer
    permissions:
      contents: read
      packages: read

    steps:

      - name: Generate app token
        id: generate_token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ vars.GIMLI_APP_ID }}
          private-key: ${{ secrets.GIMLIBOT_CERT }}
          owner: "anduintransaction"
          repositories: |
            deep-space-nine

      - name: Checkout
        uses: actions/checkout@v4
        with:
          set-safe-directory: "${{ env.GITHUB_WORKSPACE }}"
          fetch-depth: 0
          ref: "${{ inputs.ref }}"

      - name: Setup AWS CLI
        uses: anduintransaction/gondolin/actions/setup-aws-cli@master

      - name: Setup SAM CLI
        uses: aws-actions/setup-sam@v2
        with:
          use-installer: true

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 21
          registry-url: https://npm.pkg.github.com

      - name: Setup make and gettext-base
        run: |
          sudo apt-get update && sudo apt-get -y install make gettext-base

      - name: Deploy
        run: |
          ./ci/scripts/build/deploy-ts-serverless.sh
        env:
          SERVERLESS_FUNCTION: ${{ inputs.serverlessFunction }}
          REGION: ${{ inputs.region }}
          NODE_AUTH_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Publish version
        id: publishVersion
        run: |
          ./ci/scripts/build/publish-version.sh
        env:
          SERVERLESS_FUNCTION_NAME: "anduin-serverless-${{ inputs.serverlessFunction }}"
          REGION: ${{ inputs.region }}
          SERVERLESS_FUNCTION_DESCRIPTION: ${{ inputs.functionDescription }}

      - name: Create tag
        uses: actions/github-script@v7
        with:
          github-token: "${{ steps.generate_token.outputs.token }}"
          script: |
            github.rest.git.createRef({
              owner: context.repo.owner,
              repo: context.repo.repo,
              ref: "refs/tags/${{ inputs.serverlessFunction }}-${{ inputs.region }}-${{ steps.publishVersion.outputs.SERVERLESS_FUNCTION_VERSION }}",
              sha: "${{ inputs.ref || github.sha }}"
            })

      - name: Slack notification
        uses: rtCamp/action-slack-notify@v2
        continue-on-error: true
        env:
          SLACK_WEBHOOK: "${{ secrets.SLACK_WEBHOOK }}"
          SLACK_ICON: "https://avatars.githubusercontent.com/u/44036562"
          SLACK_USERNAME: Actions
          SLACK_CHANNEL: system-stuffs
          SLACK_MESSAGE: "${{ inputs.serverlessFunction }}-${{ inputs.region }}-${{ steps.publishVersion.outputs.SERVERLESS_FUNCTION_VERSION }}"
          SLACK_COLOR: "${{ job.status }}"
          SLACK_FOOTER: "Powered by Github Actions"
          SITE_TITLE: Build status
          SITE_NAME: "${{ job.status }}"
          HOST_TITLE: Repo
          HOST_NAME: deep-space-nine
