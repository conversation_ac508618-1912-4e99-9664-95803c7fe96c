name: "[CI] Build (PR)"

on:
  workflow_call:

jobs:
  run:
    runs-on:
      - heavy-runner-v2
    container:
      image: public.ecr.aws/anduintransact/anduin-runner-build:1.11.0-jammy
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          set-safe-directory: "${{ env.GITHUB_WORKSPACE }}"
          fetch-depth: 0

      - name: Pull cache
        if: ${{ ! contains(github.event.pull_request.labels.*.name, 'Cache/None') }}
        run: |
          ./ci/scripts/build/runner-copy-cache.sh pull
        env:
          BRANCH_NAME: ${{ github.base_ref || github.ref_name }}

      - name: Pull artifact
        if: ${{ ! contains(github.event.pull_request.labels.*.name, 'Cache/None') }}
        run: |
          ./ci/scripts/build/runner-copy-artifact.sh pull --remote-cache
        env:
          BRANCH_NAME: ${{ github.base_ref || github.ref_name }}

      - name: JDK info
        run: |
          java -version

      - name: Build
        run: |
          ./ci/scripts/build/build.sh
        env:
          ANDUIN_BUILD_ENV: pr

      - name: Push artifact
        run: |
          ./ci/scripts/build/runner-copy-artifact.sh push runid-${{ github.run_id }}
        env:
          BRANCH_NAME: ${{ github.base_ref || github.ref_name }}
