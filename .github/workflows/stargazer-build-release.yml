name: "Build Release"

on:
  workflow_call:
    inputs:
      build-env:
        required: true
        type: string
      artifact-folder:
        required: true
        type: string
      use-cache:
        required: false
        type: string

jobs:
  build:
    timeout-minutes: 90

    runs-on:
      - heavy-runner-v2
    container:
      image: public.ecr.aws/anduintransact/anduin-runner-build:1.11.0-jammy
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          set-safe-directory: "${{ env.GITHUB_WORKSPACE }}"
          fetch-depth: 0

      - name: Pull cache
        if: ${{ inputs.use-cache == 'true' }}
        run: |
          ./ci/scripts/build/runner-copy-cache.sh pull
        env:
          BRANCH_NAME: ${{ github.ref_name }}

      - name: Pull artifact
        if: ${{ inputs.use-cache == 'true' }}
        run: |
          ./ci/scripts/build/runner-copy-artifact.sh pull --remote-cache
        env:
          BRANCH_NAME: ${{ github.ref_name }}

      - name: JDK info
        run: |
          java -version

      - name: Build
        run: |
          ./ci/scripts/build/build.sh
        env:
          ANDUIN_BUILD_ENV: "${{ inputs.build-env }}"

      - name: Publish artifact
        run: |
          ./ci/scripts/build/publish-artifact.sh
        env:
          ARTIFACTORY_FOLDER: "${{ inputs.artifact-folder }}"
