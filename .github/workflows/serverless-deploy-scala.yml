name: "[Server<PERSON>] Deploy Scala"

on:
  workflow_call:
    inputs:
      serverlessFunction:
        required: true
        type: string
      region:
        required: true
        type: string
      ref:
        required: true
        type: string
      functionDescription:
        required: false
        type: string
      publishVersion:
        required: false
        type: boolean
        default: true

jobs:
  build-deploy:
    runs-on: deployer
    steps:

      - name: Generate app token
        id: generate_token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ vars.GIMLI_APP_ID }}
          private-key: ${{ secrets.GIMLIBOT_CERT }}
          owner: "anduintransaction"
          repositories: "deep-space-nine"

      - name: Checkout
        uses: actions/checkout@v4
        with:
          set-safe-directory: "${{ env.GITHUB_WORKSPACE }}"
          fetch-depth: 0
          ref: "${{ inputs.ref }}"

      - name: Setup Java
        uses: actions/setup-java@v4
        with:
          distribution: "temurin"
          java-version: "21"

      - name: Setup Node
        uses: actions/setup-node@v4
        with:
          node-version: 21

      - name: Setup AWS CLI
        uses: anduintransaction/gondolin/actions/setup-aws-cli@master

      - name: Setup SAM CLI
        uses: aws-actions/setup-sam@v2
        with:
          use-installer: true

      - name: Setup make
        run: |
          sudo apt-get update && sudo apt-get -y install make

      - name: Build
        run: |
          ./ci/scripts/build/build-scala-serverless.sh
        env:
          SERVERLESS_FUNCTION: ${{ inputs.serverlessFunction }}

      - name: Deploy
        run: |
          ./ci/scripts/build/deploy-scala-serverless.sh
        env:
          SERVERLESS_FUNCTION: ${{ inputs.serverlessFunction }}
          REGION: ${{ inputs.region }}

      - name: Publish version
        id: publishVersion
        if: ${{ inputs.publishVersion }}
        run: |
          ./ci/scripts/build/publish-version.sh
        env:
          SERVERLESS_FUNCTION_NAME: "anduin-serverless-${{ inputs.serverlessFunction }}"
          REGION: ${{ inputs.region }}
          SERVERLESS_FUNCTION_DESCRIPTION: ${{ inputs.functionDescription }}

      - name: Create tag
        if: ${{ inputs.publishVersion }}
        uses: actions/github-script@v7
        with:
          github-token: "${{ steps.generate_token.outputs.token }}"
          script: |
            github.rest.git.createRef({
              owner: context.repo.owner,
              repo: context.repo.repo,
              ref: "refs/tags/${{ inputs.serverlessFunction }}-${{ inputs.region }}-${{ steps.publishVersion.outputs.SERVERLESS_FUNCTION_VERSION }}",
              sha: "${{ inputs.ref || github.sha }}"
            })

      - name: Slack notification
        if: ${{ inputs.publishVersion }}
        uses: rtCamp/action-slack-notify@v2
        continue-on-error: true
        env:
          SLACK_WEBHOOK: "${{ secrets.SLACK_WEBHOOK }}"
          SLACK_ICON: "https://avatars.githubusercontent.com/u/44036562"
          SLACK_USERNAME: Actions
          SLACK_CHANNEL: system-stuffs
          SLACK_MESSAGE: "${{ inputs.serverlessFunction }}-${{ inputs.region }}-${{ steps.publishVersion.outputs.SERVERLESS_FUNCTION_VERSION }}"
          SLACK_COLOR: "${{ job.status }}"
          SLACK_FOOTER: "Powered by Github Actions"
          SITE_TITLE: Build status
          SITE_NAME: "${{ job.status }}"
          HOST_TITLE: Repo
          HOST_NAME: deep-space-nine
