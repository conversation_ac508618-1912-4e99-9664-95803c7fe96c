name: "[<PERSON>ga<PERSON>] Restart"

run-name: "Restart ${{ inputs.environment }}"

permissions:
  contents: read
  packages: read

on:
  workflow_dispatch:
    inputs:
      environment:
        required: true
        description: "Restart environment"
        type: choice
        options:
          - "gondor-eu-canary"
          - "gondor-minas-tirith"
          - "gondor-internal"
          - "gondor-demo"
          - "gondor-public"
          - "gondor-eu-public"
          - "gondor-feature"
          - "fundsub-simulator"
          - "simulator-test"

jobs:
  restart:
    runs-on:
      - deployer
    environment: "${{ inputs.environment }}"
    container:
      image: public.ecr.aws/anduintransact/deployer:*******
      options: --user 1001
      volumes:
        - /var/run/secrets/kubernetes.io/serviceaccount:/var/run/secrets/kubernetes.io/serviceaccount
      env:
        KUBERNETES_SERVICE_HOST: kubernetes.default.svc
        KUBERNETES_SERVICE_PORT: "443"
    steps:
      - name: Materialize kube config
        run: |
          mkdir -p ~/.kube
          echo "$ANDUIN_KUBE_CONFIG" | base64 -d > ~/.kube/config
        env:
          ANDUIN_KUBE_CONFIG: "${{ secrets.ANDUIN_KUBE_CONFIG }}"

      - name: Debug kubeconfig
        run: |
          kubectl config view

      - name: Restart
        run: |
          kubectl --context ${{ vars.RIVENDELL_CONTEXT }} -n ${{ inputs.environment }} \
            get deployment \
            | grep gondor \
            | awk '{print $1}' \
            | xargs -I {} kubectl --context ${{ vars.RIVENDELL_CONTEXT }} -n ${{ inputs.environment }} \
                rollout restart "deployment/{}"
