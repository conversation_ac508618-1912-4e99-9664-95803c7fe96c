name: "Release"

on:
  workflow_call:
    inputs:
      build-env:
        required: true
        type: string
      artifact-folder:
        required: true
        type: string
      image-prefix:
        required: true
        type: string
      slack-notify:
        required: false
        type: boolean
        default: false
      environment:
        required: false
        type: string
        default: default

jobs:
  build-with-cache:
    uses: anduintransaction/gondolin/.github/workflows/stargazer-build-release.yml@master
    secrets: inherit
    with:
      build-env: ${{ inputs.build-env }}
      artifact-folder: ${{ inputs.artifact-folder }}
      use-cache: 'true'

  build-without-cache:
    needs: build-with-cache
    if: always() && needs.build-with-cache.result == 'failure'
    uses: anduintransaction/gondolin/.github/workflows/stargazer-build-release.yml@master
    with:
      build-env: ${{ inputs.build-env }}
      artifact-folder: ${{ inputs.artifact-folder }}
      use-cache: 'false'

  publish-docker:
    if: ${{ always() && contains(join(needs.*.result, ','), 'success') }}
    needs:
      - build-with-cache
      - build-without-cache
    runs-on:
      - docker-builder-v2
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          set-safe-directory: "${{ env.GITHUB_WORKSPACE }}"
          fetch-depth: 0

      - name: Setup doriath
        uses: anduintransaction/gondolin/actions/setup-tools/doriath@master

      - name: Setup AWS Cli
        run: |
          curl -o /tmp/awscliv2.zip https://awscli.amazonaws.com/awscli-exe-linux-x86_64.zip
          unzip -d /tmp/ /tmp/awscliv2.zip
          sudo /tmp/aws/install

      - name: Publish
        run: |
          ./ci/scripts/build/runner-publish-docker.sh
        env:
          ARTIFACTORY_FOLDER: "${{ inputs.artifact-folder }}"
          IMAGE_PREFIX: "${{ inputs.image-prefix }}"

  publish-cdn:
    if: ${{ always() && contains(join(needs.*.result, ','), 'success') }}
    needs:
      - build-with-cache
      - build-without-cache
    runs-on: 
      - common-runner-v2
    environment: "${{ inputs.environment }}"
    steps:
      - name: Checkout
        if: ${{ vars.CDN_BUCKET }}
        uses: actions/checkout@v4
        with:
          set-safe-directory: "${{ env.GITHUB_WORKSPACE }}"
          fetch-depth: 0

      - if: ${{ vars.CDN_BUCKET }}
        uses: anduintransaction/gondolin/actions/setup-aws-cli@master

      - name: Publish
        if: ${{ vars.CDN_BUCKET }}
        run: |
          export PUBLISH_SCRIPT="./ci/scripts/build/publish-cdn.sh"
          if [ -f $PUBLISH_SCRIPT ]; then
            $PUBLISH_SCRIPT
          fi
        env:
          CDN_BUCKET: "${{ vars.CDN_BUCKET }}"
          CDN_KEY_ID: "${{ secrets.CDN_KEY_ID }}"
          CDN_KEY_SECRET: "${{ secrets.CDN_KEY_SECRET }}"
          CDN_ENDPOINT_URL: "${{ secrets.CDN_ENDPOINT_URL }}"
          CDN_TOKEN: "${{ secrets.CDN_TOKEN }}"
          ARTIFACTORY_FOLDER: "${{ inputs.artifact-folder }}"

  notification:
    if: inputs.slack-notify && always()
    needs:
      - publish-docker
      - publish-cdn
    runs-on:
      - common-runner-v2
    steps:
      - name: Checkout
        uses: actions/checkout@v4

      - name: Slack notification
        uses: rtCamp/action-slack-notify@v2
        continue-on-error: true
        env:
          SLACK_WEBHOOK: "${{ secrets.SLACK_WEBHOOK }}"
          SLACK_ICON: "https://avatars.githubusercontent.com/u/44036562"
          SLACK_USERNAME: Actions
          SLACK_CHANNEL: system-stuffs
          SLACK_COLOR: "${{ job.status }}"
          SLACK_FOOTER: "Powered by Github Actions"
          SITE_TITLE: Build status
          SITE_NAME: "${{ job.status }}"
          HOST_TITLE: Repo
          HOST_NAME: stargazer
