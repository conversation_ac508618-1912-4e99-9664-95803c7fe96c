name: "[Stargazer] Auto upgrade"

permissions:
  contents: read
  packages: read

on:
  workflow_dispatch:
  schedule:
    - cron: "14 17 * * *"

jobs:
  upgrade:
    strategy:
      fail-fast: true
      matrix:
        environment:
          - gondor-eu-canary
          - gondor-minas-tirith
    uses: ./.github/workflows/stargazer-full-upgrade.yml
    with:
      environment: ${{ matrix.environment }}
    secrets: inherit
