name: "[Remote] Upgrade"

run-name: "Remote upgrade ${{ inputs.environment }}"

permissions:
  contents: read
  packages: read

on:
  workflow_call:
    inputs:
      stargazer-branch:
        required: true
        description: "Stargazer deploy branch"
        type: string
        default: master
      environment:
        required: true
        description: "Deploy to environment"
        type: string
        default: strider
      dryrun:
        required: true
        description: "Dry run"
        type: boolean
        default: false
  workflow_dispatch:
    inputs:
      stargazer-branch:
        required: true
        description: "Stargazer deploy branch"
        type: string
        default: master
      environment:
        required: true
        description: "Deploy to environment"
        type: string
        default: strider
      dryrun:
        required: true
        description: "Dry run"
        type: boolean
        default: false

concurrency:
  group: "remote-upgrade-${{ inputs.environment }}"
  cancel-in-progress: false

jobs:
  upgrade:
    runs-on: 
      - deployer
    environment: blackwood
    container:
      image: public.ecr.aws/anduintransact/deployer:*******
      options: --user 1001
      volumes:
        - /var/run/secrets/kubernetes.io/serviceaccount:/var/run/secrets/kubernetes.io/serviceaccount
        - /run/docker:/run/docker
      env:
        DOCKER_HOST: unix:///run/docker/docker.sock
        KUBERNETES_SERVICE_HOST: kubernetes.default.svc
        KUBERNETES_SERVICE_PORT: "443"
    steps:
      - name: Generate app token
        id: generate_token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ vars.GIMLI_APP_ID }}
          private-key: ${{ secrets.GIMLIBOT_CERT }}
          owner: anduintransaction
          repositories: "stargazer"

      - name: Checkout
        uses: actions/checkout@v4
        with:
          repository: anduintransaction/stargazer
          ref: "${{ inputs.stargazer-branch }}"
          token: "${{ steps.generate_token.outputs.token }}"

      - uses: actions/setup-node@v4
        with:
          node-version: 21

      - name: Materialize kube config
        run: |
          mkdir -p ~/.kube
          echo "$ANDUIN_KUBE_CONFIG" | base64 -d > ~/.kube/config
        env:
          ANDUIN_KUBE_CONFIG: "${{ secrets.BLACKWOOD_KUBE_CONFIG }}"

      - name: Debug kubeconfig
        run: |
          kubectl config view

      - name: Run Cloudflared
        run: |
          cloudflared access tcp --hostname blackwood-sg01.anduin.fund --url 127.0.0.1:6644 &
        env:
          TUNNEL_SERVICE_TOKEN_ID: "${{ secrets.CLOUDFLARED_CLIENT_ID }}"
          TUNNEL_SERVICE_TOKEN_SECRET: "${{ secrets.CLOUDFLARED_CLIENT_SECRET }}"

      - name: Upgrade
        run: |
          ./ci/scripts/deploy/remote-dev.sh ${{ inputs.environment }}
        env:
          GH_NPM_READ_TOKEN: "${{ github.token }}"
          RIVENDELL_CONTEXT: "${{ vars.RIVENDELL_CONTEXT }}"
          DRY_RUN: "${{ inputs.dryrun }}"
