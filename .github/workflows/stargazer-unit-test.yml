name: "[CI] Unit test"

on:
  workflow_call:

jobs:
  run:
    runs-on:
      - heavy-runner-v2
    container:
      image: public.ecr.aws/anduintransact/anduin-runner-build:1.11.0-jammy
      volumes:
        - /run/docker:/run/docker
      env:
        DOCKER_HOST: unix:///run/docker/docker.sock
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          set-safe-directory: "${{ env.GITHUB_WORKSPACE }}"

      - name: Export namespace
        run: |
          export NAMESPACE_PREFIX="spr-${{ github.event.pull_request.number }}"
          echo "NAMESPACE_PREFIX=$NAMESPACE_PREFIX" >> $GITHUB_ENV
          echo "NAMESPACE=$NAMESPACE_PREFIX-$(date +%s)" >> $GITHUB_ENV

      - name: JDK info
        run: |
          java -version

      - name: Pull cache
        run: |
          ./ci/scripts/build/runner-copy-cache.sh pull
        env:
          BRANCH_NAME: ${{ github.base_ref || github.ref_name }}

      - name: Pull artifact
        run: |
          ./ci/scripts/build/runner-copy-artifact.sh pull runid-${{ github.run_id }}
        env:
          BRANCH_NAME: ${{ github.base_ref || github.ref_name }}

      - name: Unit test
        run: |
          ./ci/scripts/build/runner-unit-test.sh
        env:
          ANDUIN_BUILD_ENV: pr

      - name: Report
        uses: mikepenz/action-junit-report@v5
        if: success() || failure()
        with:
          check_name: civ3/unit-test
          report_paths: ./out/**/unit-test-report.xml

      - name: Report S3
        uses: anduintransaction/gondolin/actions/junit-analytics@master
        if: success() || failure()
        continue-on-error: true
        with:
          report_paths: ./out/**/unit-test-report.xml
          s3_bucket: stargazer-github-actions
          s3_dir: ci/raw
          json_fn: runid-${{ github.run_id }}-unit-test.json
