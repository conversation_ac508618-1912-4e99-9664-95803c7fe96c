name: "[CI] Build (PR)"

on:
  workflow_call:

jobs:
  run:
    runs-on:
      - common-runner-v2
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          set-safe-directory: "${{ env.GITHUB_WORKSPACE }}"
          fetch-depth: 0

      - name: Install uv
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version-file: .python-version

      - name: Linter
        run: |
          uvx ruff check packages/

      - name: Check format
        run: |
          uvx ruff format --check packages/

      - name: Check typing
        run: |
          uv sync --all-packages && uv run pyright

      - name: Unit test
        run: |
          ./ci/scripts/unit-test.sh

      - name: Package test
        run: |
          ./ci/scripts/package-test.sh
