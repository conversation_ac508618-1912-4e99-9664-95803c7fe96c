name: "Release"

on:
  workflow_call:

jobs:
  publish-artifact:
    runs-on:
      - heavy-runner-v2
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          set-safe-directory: "${{ env.GITHUB_WORKSPACE }}"
          fetch-depth: 0

      - name: Setup java
        uses: actions/setup-java@v3
        with:
          distribution: "temurin"
          java-version: "17"

      - name: Build
        run: |
          ./ci/scripts/build/build-ci.sh

      - name: Publish artifact
        run: |
          ./ci/scripts/build/publish-artifact.sh
        env:
          ARTIFACTORY_FOLDER: bifrost

  publish-docker:
    needs:
      - publish-artifact
    runs-on:
      - docker-builder-v2
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          set-safe-directory: "${{ env.GITHUB_WORKSPACE }}"
          fetch-depth: 0

      - name: Setup aws cli
        uses: anduintransaction/gondolin/actions/setup-aws-cli@master

      - name: Setup doriath
        uses: anduintransaction/gondolin/actions/setup-tools/doriath@master

      - name: Acquire git tag
        id: gitvers
        run: |
          echo "APP_VERSION=$(git describe --tags --exact-match 2> /dev/null || git describe --tags --abbrev=7 | rev | cut -c 2-7,9- | rev)" >> $GITHUB_OUTPUT

      - name: Publish
        run: |
          ./ci/scripts/build/publish-docker.sh
        env:
          ARTIFACTORY_FOLDER: bifrost
          IMAGE_PREFIX: bifrost

      - name: Slack notification
        uses: rtCamp/action-slack-notify@v2
        continue-on-error: true
        env:
          SLACK_WEBHOOK: "${{ secrets.SLACK_WEBHOOK }}"
          SLACK_ICON: "https://avatars.githubusercontent.com/u/44036562"
          SLACK_USERNAME: Actions
          SLACK_CHANNEL: system-stuffs
          SLACK_MESSAGE: "Version: `${{ steps.gitvers.outputs.APP_VERSION }}`"
          SLACK_COLOR: "${{ job.status }}"
          SLACK_FOOTER: "Powered by Github Actions"
          SITE_TITLE: Build status
          SITE_NAME: "${{ job.status }}"
          HOST_TITLE: Repo
          HOST_NAME: bifrost
