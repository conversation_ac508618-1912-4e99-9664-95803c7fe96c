name: "[CI] Secret scan"

on:
  workflow_call:

jobs:
  run:
    runs-on:
      - common-runner-v2
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          set-safe-directory: "${{ env.GITHUB_WORKSPACE }}"

      - name: Install trufflehog
        run: |
          curl -sSfL https://raw.githubusercontent.com/trufflesecurity/trufflehog/main/scripts/install.sh | sudo sh -s -- -b /usr/local/bin v3.26.0

      - name: Secret scan
        if: false
        run: |
          trufflehog git file://. --no-verification --no-update --max-depth=1
