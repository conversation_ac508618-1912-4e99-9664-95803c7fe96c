name: "Nuke fundsub-simulator"

permissions:
  contents: read
  packages: read

on:
  schedule:
    - cron: "00 2 * * 3"

jobs:

  blocker-check:
    runs-on:
      - common-runner-v2
    steps:
      - name: Blocker check
        uses: anduintransaction/gondolin/actions/jira-blocker-check@master
        with:
          token: ${{ secrets.JIRA_BLOCKER_TOKEN }}
          filter_id: "10213"

      - name: Announce failure
        uses: anduintransaction/gondolin/actions/simple-slack-notification@master
        if:  ${{ failure() }}
        with:
          webhook-url: ${{ secrets.SLACK_WEBHOOK }}
          channel: release-squad
          color: danger
          title: "Jira blocker issue"
          message: "<!here> skip stargazer fundsub-simulator deployment due to blocker issue exists. Link: <https://anduin.atlassian.net/issues/?filter=10213>"

  upgrade:
    needs: ["blocker-check"]
    uses: ./.github/workflows/simulator-nuke.yml
    with:
      environment: "fundsub-simulator"
    secrets: inherit
