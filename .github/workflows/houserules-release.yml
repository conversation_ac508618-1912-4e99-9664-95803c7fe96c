name: "Release"

on:
  workflow_call:

jobs:
  run:
    runs-on:
      - common-runner-v2
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          set-safe-directory: "${{ env.GITHUB_WORKSPACE }}"
          fetch-depth: 0

      - uses: actions/setup-java@v4
        with:
          distribution: "temurin"
          java-version: "21"

      - uses: anduintransaction/gondolin/actions/setup-aws-cli@master

      - name: Acquire git tag
        id: gitvers
        run: |
          echo "APP_VERSION=$(git describe --tags --abbrev=7)" >> $GITHUB_OUTPUT

      - name: Pull cache
        run: |
          ./ci/scripts/build/runner-copy-cache.sh pull

      - name: Build
        run: |
          ./ci/scripts/build/build.sh
        env:
          ANDUIN_BUILD_ENV: canary

      - name: Publish
        run: |
          ./ci/scripts/build/publish-artifact.sh
        env:
          ANDUIN_BUILD_ENV: canary

      - name: Slack notification
        uses: rtCamp/action-slack-notify@v2
        continue-on-error: true
        env:
          SLACK_WEBHOOK: "${{ secrets.SLACK_WEBHOOK }}"
          SLACK_ICON: "https://avatars.githubusercontent.com/u/44036562"
          SLACK_USERNAME: Actions
          SLACK_CHANNEL: system-stuffs
          SLACK_MESSAGE: "Version: `${{ steps.gitvers.outputs.APP_VERSION }}`"
          SLACK_COLOR: "${{ job.status }}"
          SLACK_FOOTER: "Powered by Github Actions"
          SITE_TITLE: Build status
          SITE_NAME: "${{ job.status }}"
          HOST_TITLE: Repo
          HOST_NAME: houserules
