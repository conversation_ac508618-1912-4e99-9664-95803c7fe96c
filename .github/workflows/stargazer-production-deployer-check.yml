name: "[<PERSON>gazer] Production deployer check"

run-name: "Production deployer check"

permissions:
  contents: read
  packages: read

on:
  workflow_call:
  workflow_dispatch:

jobs:

  production-deployer-check:
    runs-on:
      - common-runner-v2
    steps:
      - name: Generate app token
        id: generate_token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ vars.GIMLI_APP_ID }}
          private-key: ${{ secrets.GIMLIBOT_CERT }}
          owner: anduintransaction
          repositories: "gondolin"

      - name: Check deployer permissions
        id: permission_check
        uses: actions/github-script@v7
        with:
          github-token: "${{ steps.generate_token.outputs.token }}"
          script: |
            try {
              const response = await github.rest.repos.getCollaboratorPermissionLevel({
                owner: "anduintransaction",
                repo: "gondolin",
                username: "${{ github.actor }}",
              });

              console.log(`[DEBUG] Response: ${JSON.stringify(response.data)}`);

              const permission = response.data.permission;
              console.log(`The actor ${{ github.actor }} has a permission level of: ${permission}`);

              // Check if user has admin permissions or is in QA_PRODUCTION_DEPLOYERS list
              const isAdmin = permission === 'admin';
              const qaDeployers = '${{ vars.QA_PRODUCTION_DEPLOYERS }}';
              const qaDeployersList = qaDeployers ? qaDeployers.split(',').map(user => user.trim()) : [];
              const isQaDeployer = qaDeployersList.includes('${{ github.actor }}');
              const hasPermission = isAdmin || isQaDeployer;

              core.setOutput('actor_permission', permission);
              core.setOutput('has_deploy_permission', hasPermission);
              core.setOutput('is_admin', isAdmin);
              core.setOutput('is_qa_deployer', isQaDeployer);

              console.log(`[DEBUG] Permission: ${permission}`);
              console.log(`[DEBUG] Is admin: ${isAdmin}`);
              console.log(`[DEBUG] QA deployers list: ${qaDeployersList.join(', ')}`);
              console.log(`[DEBUG] Is QA deployer: ${isQaDeployer}`);
              console.log(`[DEBUG] Has deploy permission: ${hasPermission}`);

              if (!hasPermission) {
                core.setFailed(`User ${{ github.actor }} does not have sufficient permissions for production deployment. User must be either an admin or listed in QA_PRODUCTION_DEPLOYERS variable. Current permission: ${permission}, QA deployers: ${qaDeployersList.join(', ')}`);
              }

            } catch (error) {
              console.error(`Error checking permissions: ${error.message}`);
              core.setFailed(`Failed to check deployer permissions: ${error.message}`);
            }
