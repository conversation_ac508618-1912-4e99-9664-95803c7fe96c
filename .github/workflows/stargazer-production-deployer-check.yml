name: "[<PERSON>gazer] Production deployer check"

run-name: "Production deployer check"

permissions:
  contents: read
  packages: read

on:
  workflow_call:
    inputs:
      root_event_name:
        required: true
        type: string
      environment:
        required: true
        type: string 
  workflow_dispatch:

jobs:

  production-deployer-check:
    runs-on:
      - common-runner-v2
    steps:
      - name: Generate app token
        id: generate_token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ vars.GIMLI_APP_ID }}
          private-key: ${{ secrets.GIMLIBOT_CERT }}
          owner: anduintransaction
          repositories: "gondolin"

      - name: Check deployer permissions
        id: permission_check
        uses: actions/github-script@v7
        with:
          github-token: "${{ steps.generate_token.outputs.token }}"
          script: |
            try {
              // Short circuit conditions
              const rootEventName = '${{ inputs.root_event_name }}';
              const environment = '${{ inputs.environment }}';

              console.log(`[DEBUG] Root event name: ${rootEventName}`);
              console.log(`[DEBUG] Environment: ${environment}`);

              // Short circuit 1: If root_event_name is "schedule", set hasPermission to true
              if (rootEventName === 'schedule') {
                console.log(`[DEBUG] Short circuit: Root event is schedule, granting permission`);
                const hasPermission = true;

                core.setOutput('actor_permission', 'schedule-bypass');
                core.setOutput('has_deploy_permission', hasPermission);
                core.setOutput('is_admin', false);
                core.setOutput('is_qa_deployer', false);

                console.log(`[DEBUG] Has deploy permission (schedule bypass): ${hasPermission}`);
                return;
              }

              // Short circuit 2: If environment is not gondor-public or gondor-eu-public, set hasPermission to true
              if (environment !== 'gondor-public' && environment !== 'gondor-eu-public') {
                console.log(`[DEBUG] Short circuit: Environment ${environment} is not production, granting permission`);
                const hasPermission = true;

                core.setOutput('actor_permission', 'non-production-bypass');
                core.setOutput('has_deploy_permission', hasPermission);
                core.setOutput('is_admin', false);
                core.setOutput('is_qa_deployer', false);

                console.log(`[DEBUG] Has deploy permission (non-production bypass): ${hasPermission}`);
                return;
              }

              const response = await github.rest.repos.getCollaboratorPermissionLevel({
                owner: "anduintransaction",
                repo: "gondolin",
                username: "${{ github.actor }}",
              });

              console.log(`[DEBUG] Response: ${JSON.stringify(response.data)}`);

              const permission = response.data.permission;
              console.log(`The actor ${{ github.actor }} has a permission level of: ${permission}`);

              // Check if user has admin permissions or is in QA_PRODUCTION_DEPLOYERS list
              const isAdmin = permission === 'admin';
              const qaDeployers = '${{ vars.QA_PRODUCTION_DEPLOYERS }}';
              const qaDeployersList = qaDeployers ? qaDeployers.split(',').map(user => user.trim()) : [];
              const isQaDeployer = qaDeployersList.includes('${{ github.actor }}');
              const hasPermission = isAdmin || isQaDeployer;

              core.setOutput('actor_permission', permission);
              core.setOutput('has_deploy_permission', hasPermission);
              core.setOutput('is_admin', isAdmin);
              core.setOutput('is_qa_deployer', isQaDeployer);

              console.log(`[DEBUG] Permission: ${permission}`);
              console.log(`[DEBUG] Is admin: ${isAdmin}`);
              console.log(`[DEBUG] QA deployers list: ${qaDeployersList.join(', ')}`);
              console.log(`[DEBUG] Is QA deployer: ${isQaDeployer}`);
              console.log(`[DEBUG] Has deploy permission: ${hasPermission}`);

              if (!hasPermission) {
                core.setFailed(`User ${{ github.actor }} does not have sufficient permissions for production deployment. User must be either an admin or listed in QA_PRODUCTION_DEPLOYERS variable. Current permission: ${permission}, QA deployers: ${qaDeployersList.join(', ')}`);
              }

            } catch (error) {
              console.error(`Error checking permissions: ${error.message}`);
              core.setFailed(`Failed to check deployer permissions: ${error.message}`);
            }
