name: "[Simulator] Nuke"

run-name: "Nuke ${{ inputs.environment }}"

permissions:
  contents: read
  packages: read

on:
  workflow_call:
    inputs:
      environment:
        required: true
        description: "Deploy to environment"
        type: string
  workflow_dispatch:
    inputs:
      environment:
        required: true
        description: "Deploy to environment"
        type: choice
        options:
          - "fundsub-simulator"
          - "simulator-test"
          - "gondor-feature"

jobs:
  nuke:
    runs-on: 
      - deployer
    environment: "${{ inputs.environment }}"
    container:
      image: public.ecr.aws/anduintransact/deployer:*******
      options: --user 1001
      volumes:
        - /var/run/secrets/kubernetes.io/serviceaccount:/var/run/secrets/kubernetes.io/serviceaccount
        - /run/docker:/run/docker
      env:
        DOCKER_HOST: unix:///run/docker/docker.sock
        KUBERNETES_SERVICE_HOST: kubernetes.default.svc
        KUBERNETES_SERVICE_PORT: "443"
    steps:
      - name: Materialize kube config
        run: |
          mkdir -p ~/.kube
          echo "$ANDUIN_KUBE_CONFIG" | base64 -d > ~/.kube/config
        env:
          ANDUIN_KUBE_CONFIG: "${{ secrets.ANDUIN_KUBE_CONFIG }}"

      - name: Debug kubeconfig
        run: |
          kubectl config view

      - name: Nuke old environment
        run: |
          kubectl --context ${{ vars.RIVENDELL_CONTEXT }} \
            delete ns --ignore-not-found ${{ inputs.environment }}

  upgrade:
    uses: ./.github/workflows/stargazer-upgrade.yml
    with:
      environment: ${{ inputs.environment }}
      dryrun: false
    secrets: inherit
    needs:
      - nuke

  announce:
    needs: ["upgrade"]
    runs-on:
      - common-runner-v2
    environment: "${{ inputs.environment }}"
    container:
      image: public.ecr.aws/anduintransact/deployer:*******
      options: --user 1001
      volumes:
        - /var/run/secrets/kubernetes.io/serviceaccount:/var/run/secrets/kubernetes.io/serviceaccount
      env:
        KUBERNETES_SERVICE_HOST: kubernetes.default.svc
        KUBERNETES_SERVICE_PORT: "443"
    steps:
      - name: Materialize kube config
        run: |
          mkdir -p ~/.kube
          echo "$ANDUIN_KUBE_CONFIG" | base64 -d > ~/.kube/config
        env:
          ANDUIN_KUBE_CONFIG: "${{ secrets.ANDUIN_KUBE_CONFIG }}"

      - name: Debug kubeconfig
        run: |
          kubectl config view

      - name: Extract image tag
        id: image-extract
        run: |
          imageTag=$(kubectl --context ${{ vars.RIVENDELL_CONTEXT }} \
            -n "${{ inputs.environment }}" \
            get deployments gondor -o json \
            | jq -r '.spec.template.spec.containers[0].image' \
            | cut -d':' -f2)
          echo "IMAGE_TAG=${imageTag}" >> "$GITHUB_OUTPUT"
      - name: Announce
        uses: anduintransaction/gondolin/actions/simple-slack-notification@master
        with:
          webhook-url: ${{ secrets.SLACK_WEBHOOK }}
          channel: release-squad
          title: "Sandbox release announcement"
          message: "<!here> Image \\`${{ steps.image-extract.outputs.IMAGE_TAG }}\\` was released into \\`${{ inputs.environment }}\\`"
