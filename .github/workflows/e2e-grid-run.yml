name: "[Selenium Grid] Anduin E2E test"

on:
  workflow_call:
    inputs:
      branch:
        required: true
        type: string
      server:
        required: true
        type: string
      param:
        required: false
        type: string
      qa_token:
        required: false
        type: string
      qa_token_eu:
        required: false
        type: string
      build_name:
        type: string
        required: true
      custom_config:
        type: string
        required: false
      browser:
        required: true
        type: string
      browser_version:
        required: true
        type: string
      record_video:
        required: false
        type: string
      app:
        required: false
        type: string

jobs:
  run:
    runs-on:
      - e2e-runner

    container:
      image: public.ecr.aws/anduintransact/anduin-runner-build:1.11.0-jammy
      options: --user 1001
      volumes:
        - /var/run/secrets/kubernetes.io/serviceaccount:/var/run/secrets/kubernetes.io/serviceaccount
        - /run/docker:/run/docker
      env:
        DOCKER_HOST: unix:///run/docker/docker.sock
        KUBERNETES_SERVICE_HOST: kubernetes.default.svc
        KUBERNETES_SERVICE_PORT: "443"

    timeout-minutes: 720

    steps:

      - name: Checkout master
        uses: actions/checkout@v4
        with:
          fetch-depth: 0
          ref: master

      - name: Set up JDK 11
        uses: actions/setup-java@v4
        with:
          java-version: '11'
          distribution: 'temurin'

      - name: Setup Maven
        uses: stCarolas/setup-maven@v5

      - name: Install cloudflare-warp
        run: |
          curl -fsSL https://pkg.cloudflareclient.com/pubkey.gpg | sudo gpg --yes --dearmor --output /usr/share/keyrings/cloudflare-warp-archive-keyring.gpg
          echo "deb [signed-by=/usr/share/keyrings/cloudflare-warp-archive-keyring.gpg] https://pkg.cloudflareclient.com/ $(lsb_release -cs) main" | sudo tee /etc/apt/sources.list.d/cloudflare-client.list
          sudo apt-get update && sudo apt-get -y install cloudflare-warp

      - name: Configure cloudflare-warp
        run: |
          # Create the cloudflare-warp directory if it doesn't exist
          sudo mkdir -p /var/lib/cloudflare-warp

          # Create the MDM configuration file
          sudo tee /var/lib/cloudflare-warp/mdm.xml > /dev/null <<EOF
          <dict>
            <key>organization</key>
            <string>anduin</string>
            <key>auth_client_id</key>
            <string>${{ secrets.SERVICE_TOKEN_KEY }}</string>
            <key>auth_client_secret</key>
            <string>${{ secrets.SERVICE_TOKEN_SECRET }}</string>
            <key>onboarding</key>
            <string>false</string>
          </dict>
          EOF

          # Set proper permissions
          sudo chmod 600 /var/lib/cloudflare-warp/mdm.xml
          sudo chown root:root /var/lib/cloudflare-warp/mdm.xml

      - name: Connect to WARP
        continue-on-error: true
        run: |
          # Connect to WARP
          sudo warp-cli connect

          # Wait for connection to establish
          echo "Waiting for WARP connection to establish..."
          for i in {1..30}; do
            if sudo warp-cli status | grep -q "Connected"; then
              echo "WARP connected successfully!"
              break
            fi
            echo "Attempt $i: Still connecting..."
            sleep 2
          done

          # Verify connection status
          sudo warp-cli status

          # Test connectivity (optional)
          echo "Testing connectivity through WARP..."
          curl -s https://www.cloudflare.com/cdn-cgi/trace | grep warp

      - name: Test connection WARP
        continue-on-error: true
        run: |
          curl -vX GET https://httpbin.anduin.center

      - name: Checkout test branch
        run: |
          ./ci/scripts/build/checkout.sh
        env:
          INPUT_BRANCH: ${{ inputs.branch }}

      - name: Build
        run: |
          ./ci/scripts/build/build.sh

      # TODO(huynguyen): clean debug log after testing phase
      - name: Provide Selenium Grid
        run: |
          mkdir "$GITHUB_WORKSPACE/target/downloaded_files"
          sudo chown 1200:1201 "$GITHUB_WORKSPACE/target/downloaded_files"
          ls -lah "$GITHUB_WORKSPACE/target/downloaded_files"
          docker run --rm -d -p 4444:4444 -p 7900:7900 --network ${{ job.container.network }} --network-alias standalone-selenium --shm-size="2g" --name selenium -v "${{ github.workspace }}/target/downloaded_files":"/home/<USER>/Downloads"  selenium/standalone-${{inputs.browser}}:${{ inputs.browser_version }}
          c_id=$(docker ps | grep "standalone-chrome" | awk '{print $1}')
          sleep 15
 
      # TODO(huynguyen): clean debug log after testing phase
      - name: Provide Selenium Video
        if: ${{ inputs.record_video == 'true' }}
        run: |
          mkdir "$GITHUB_WORKSPACE/videos"
          sudo chown 1200:1201 "$GITHUB_WORKSPACE/videos"
          docker run --rm -d --network ${{ job.container.network }} --name video -v ${{ github.workspace }}/videos:/videos selenium/video:ffmpeg-6.1.1-20240621
          docker ps
          docker inspect video
          sleep 15

      - name: Run test
        timeout-minutes: 700
        run: |
          ./ci/scripts/build/test.grid.sh
        env:
          INPUT_SERVER: ${{ inputs.server }}
          INPUT_QA_TOKEN: ${{ inputs.qa_token }}
          INPUT_QA_TOKEN_EU: ${{ inputs.qa_token_eu }}
          INPUT_PARAM: ${{ inputs.param }}
          INPUT_BROWSER: ${{ inputs.browser }}
          INPUT_BROWSER_VERSION: ${{ inputs.browser_version }}
          INPUT_CUSTOM_CONFIG: ${{ inputs.custom_config }}
          BROWSER_STACK_KEY: ${{ secrets.BROWSER_STACK_KEY_TRIAL}}
          BROWSER_STACK_SECRET: ${{ secrets.BROWSER_STACK_SECRET_TRIAL}}
          INPUT_APP: ${{ inputs.app }}
          #INPUT_CLASS: ${{ matrix.class }}

      - name: Generate report and set report summary
        if: success() || failure()
        id: report_info
        continue-on-error: true
        run: |
          ./ci/scripts/build/generate-report.grid.sh pushResult
        env:
          INPUT_SERVER: ${{ inputs.server }}
          INPUT_BUILD_NAME: ${{ inputs.build_name }}
          WORKFLOW_RUN_ID: ${{ github.run_id }}
          R2_CLOUDFLARE_KEY: ${{ secrets.R2_CLOUDFLARE_KEY }}
          R2_CLOUDFLARE_SECRET: ${{ secrets.R2_CLOUDFLARE_SECRET }}
          R2_CLOUDFLARE_URL: ${{ secrets.R2_CLOUDFLARE_URL }}

      # TODO(huynguyen): clean debug log after testing phase
      - name: Debug Selenium Grid
        run: |
          ls -lah "$GITHUB_WORKSPACE/target/downloaded_files"
          echo "Downloads folder:"
          docker exec selenium ls -lah /home/<USER>/Downloads

      - name: Stop container Selenium Grid
        run: |
          docker stop selenium
          sleep 30

      # TODO(huynguyen): clean debug log after testing phase
      - name: Debug Selenium video
        if: ${{ inputs.record_video == 'true' }}
        run: |
          echo "Videos folder:"
          docker exec video ls -lah /videos
          ls -lah "$GITHUB_WORKSPACE/videos"
          echo "$GITHUB_WORKSPACE/videos"

      - name: Stop container video
        if: ${{ inputs.record_video == 'true' }}
        run: |
          docker stop video
          sleep 30

      - name: Upload video
        if: ${{ inputs.record_video == 'true' }}
        uses: actions/upload-artifact@v4
        with:
          name: video
          path: |
            ${{ github.workspace }}/videos/*
