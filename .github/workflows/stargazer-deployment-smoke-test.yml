name: "[<PERSON><PERSON><PERSON>] Deployment smoke test"

run-name: "Smoke test ${{ inputs.environment }}"

permissions:
  contents: read
  packages: read

on:
  workflow_call:
    inputs:
      environment:
        required: true
        description: "Deploy to environment"
        type: string
      dryrun:
        required: true
        description: "Dry run"
        type: boolean
        default: false
  workflow_dispatch:
    inputs:
      environment:
        required: true
        description: "Deploy to environment"
        type: choice
        options:
          - "gondor-eu-canary"
          - "gondor-minas-tirith"
          - "gondor-internal"
          - "gondor-demo"
          - "gondor-public"
          - "gondor-eu-public"
          - "gondor-feature"
          - "fundsub-simulator"
          - "simulator-test"
      dryrun:
        required: true
        description: "Dry run"
        type: boolean
        default: false

concurrency:
  group: "smoketest-${{ inputs.environment }}"
  cancel-in-progress: false

jobs:
  trigger-public-api-test:
    if: ${{ inputs.environment == 'gondor-demo' }}
    continue-on-error: true
    runs-on:
      - common-runner-v2
    steps:
      - name: Generate app token
        id: generate_token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ vars.GIMLI_APP_ID }}
          private-key: ${{ secrets.GIMLIBOT_CERT }}
          owner: anduintransaction
          repositories: "public-api-auto-test"
  
      - uses: actions/github-script@v7
        with:
          github-token: "${{ steps.generate_token.outputs.token }}"
          script: |
            await github.rest.actions.createWorkflowDispatch({
              owner: 'anduintransaction',
              repo: 'public-api-auto-test',
              workflow_id: 'public-api.yml',
              ref: 'master',
              inputs: {
                server: "${{ inputs.environment }}",
              },
            })

  static-smoketest:
    runs-on:
      - deployer
    environment: "${{ inputs.environment }}"
    container:
      image: public.ecr.aws/anduintransact/deployer:*******
      options: --user 1001
      volumes:
        - /var/run/secrets/kubernetes.io/serviceaccount:/var/run/secrets/kubernetes.io/serviceaccount
        - /run/docker:/run/docker
      env:
        DOCKER_HOST: unix:///run/docker/docker.sock
        KUBERNETES_SERVICE_HOST: kubernetes.default.svc
        KUBERNETES_SERVICE_PORT: "443"
    steps:
      - name: Generate app token
        id: generate_token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ vars.GIMLI_APP_ID }}
          private-key: ${{ secrets.GIMLIBOT_CERT }}
          owner: anduintransaction
          repositories: "stargazer"

      - name: Checkout
        uses: actions/checkout@v4
        with:
          repository: anduintransaction/stargazer
          ref: "${{ vars.STARGAZER_BRANCH }}"
          token: "${{ steps.generate_token.outputs.token }}"

      - uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Materialize kube config
        run: |
          mkdir -p ~/.kube
          echo "$ANDUIN_KUBE_CONFIG" | base64 -d > ~/.kube/config
        env:
          ANDUIN_KUBE_CONFIG: "${{ secrets.ANDUIN_KUBE_CONFIG }}"

      - name: Debug kubeconfig
        run: |
          kubectl config view

      - name: Smoke test
        run: |
          ./ci/scripts/deploy/smoke-test.sh \
            "${{ inputs.environment }}" \
            "${{ vars.STARGAZER_IMAGE }}"
        env:
          GH_NPM_READ_TOKEN: "${{ github.token }}"
          RIVENDELL_CONTEXT: "${{ vars.RIVENDELL_CONTEXT }}"
          RIVENDELL_USE_V2: "true"
          DRY_RUN: "${{ inputs.dryrun }}"
          ANDUIN_INFISICAL_CLIENT_ID: "${{ secrets.ANDUIN_INFISICAL_CLIENT_ID }}"
          ANDUIN_INFISICAL_CLIENT_SECRET: "${{ secrets.ANDUIN_INFISICAL_CLIENT_SECRET }}"

      - name: Logs
        if: always()
        run: |
          kubectl wait \
            -n ${{ inputs.environment }} --context ${{ vars.RIVENDELL_CONTEXT }} \
            --for=jsonpath='{.status.phase}'=Running \
            pod --selector=job-name=smoke-test --timeout=120s
          
          kubectl logs -f  \
            -n ${{ inputs.environment }} --context ${{ vars.RIVENDELL_CONTEXT }} \
            job/smoke-test 

      - name: Check status
        run: |
          sleep 5
          kubectl --context ${{ vars.RIVENDELL_CONTEXT }} -n ${{ inputs.environment }} \
            get pod --selector=job-name=smoke-test -o json \
            | jq .items[0].status.phase \
            | grep "Succeeded"

      - name: Announce failure
        uses: anduintransaction/gondolin/actions/simple-slack-notification@master
        if: ${{ failure() }}
        with:
          webhook-url: ${{ secrets.SLACK_WEBHOOK }}
          channel: release-squad
          color: danger
          title: "Smoke test failed"
          message: "<!here> Smoke test failed for ${{ inputs.environment }}"

  infra-smoketest:
    if: ${{ inputs.environment == 'gondor-public' || inputs.environment == 'gondor-eu-public' }}
    continue-on-error: true
    runs-on:
      - common-runner-v2
    steps:
      - name: Generate app token
        id: generate_token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ vars.GIMLI_APP_ID }}
          private-key: ${{ secrets.GIMLIBOT_CERT }}
          owner: anduintransaction
          repositories: "infra-smoke-test"
  
      - uses: actions/github-script@v7
        with:
          github-token: "${{ steps.generate_token.outputs.token }}"
          script: |
            await github.rest.actions.createWorkflowDispatch({
              owner: 'anduintransaction',
              repo: 'infra-smoke-test',
              workflow_id: 'test-single-environment.yaml',
              ref: 'main',
              inputs: {
                environment: "${{ inputs.environment }}",
              },
            })
