name: "[CI] Build (PR)"

on:
  workflow_call:

jobs:
  run:
    runs-on:
      - common-runner-v2
    steps:
      - name: Generate app token
        id: generate_token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ vars.GIMLI_APP_ID }}
          private-key: ${{ secrets.GIMLIBOT_CERT }}
          owner: "anduintransaction"
          repositories: |
            horus
            aule-forge

      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          set-safe-directory: "${{ env.GITHUB_WORKSPACE }}"
          fetch-depth: 0

      - name: Install uv
        uses: astral-sh/setup-uv@v5
        with:
          enable-cache: true

      - name: Setup Python
        uses: actions/setup-python@v5
        with:
          python-version-file: .python-version

      - name: CI check
        run: |
          ./ci/scripts/ci-check.sh
        env:
          GH_TOKEN: git:${{ steps.generate_token.outputs.token }}

      - name: Unit test
        run: |
          ./ci/scripts/unit-test.sh
        env:
          GH_TOKEN: git:${{ steps.generate_token.outputs.token }}
