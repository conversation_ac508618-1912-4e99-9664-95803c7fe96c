name: "[<PERSON>gazer] Hotfix Migration"

run-name: "Hotfix Migrate ${{ inputs.environment }}"

permissions:
  contents: read
  packages: read

on:
  workflow_call:
    inputs:
      environment:
        required: true
        description: "Deploy to environment"
        type: string
      dryrun:
        required: true
        description: "Dry run"
        type: boolean
        default: false
  workflow_dispatch:
    inputs:
      environment:
        required: true
        description: "Deploy to environment"
        type: choice
        options:
          - "gondor-eu-canary"
          - "gondor-minas-tirith"
          - "gondor-internal"
          - "gondor-demo"
          - "gondor-public"
          - "gondor-eu-public"
          - "gondor-feature"
      dryrun:
        required: true
        description: "Dry run"
        type: boolean
        default: false

concurrency:
  group: "upgrade-${{ inputs.environment }}"
  cancel-in-progress: false

jobs:

  production-deployer-check:
    if: ${{ github.event_name == 'workflow_dispatch' && (inputs.environment == 'gondor-public' || inputs.environment == 'gondor-eu-public') }}
    uses: ./.github/workflows/stargazer-production-deployer-check.yml
    secrets: inherit

  production-release-blocker:
    runs-on: deployer
    environment: "${{ inputs.environment }}"
    steps:
      - name: Checkout Gondolin
        uses: actions/checkout@v4

      - name: Production release blocker check
        if: ${{ inputs.environment == 'gondor-public' || inputs.environment == 'gondor-eu-public' }}
        run: |
          ./scripts/stargazer-production-release-blocker-check.sh
        env:
          INFRA_RELEASE_BLOCKER: "${{ vars.INFRA_RELEASE_BLOCKER }}"

  migrate:
    needs:
      - production-release-blocker
      - production-deployer-check
    if: ${{ always() && needs.production-release-blocker.result == 'success' && (needs.production-deployer-check.result == 'success' || needs.production-deployer-check.result == 'skipped') }}
    runs-on:
      - deployer
    environment: "${{ inputs.environment }}"
    container:
      image: public.ecr.aws/anduintransact/deployer:*******
      options: --user 1001
      volumes:
        - /var/run/secrets/kubernetes.io/serviceaccount:/var/run/secrets/kubernetes.io/serviceaccount
        - /run/docker:/run/docker
      env:
        DOCKER_HOST: unix:///run/docker/docker.sock
        KUBERNETES_SERVICE_HOST: kubernetes.default.svc
        KUBERNETES_SERVICE_PORT: "443"
    steps:
      - name: Generate app token
        id: generate_token
        uses: actions/create-github-app-token@v1
        with:
          app-id: ${{ vars.GIMLI_APP_ID }}
          private-key: ${{ secrets.GIMLIBOT_CERT }}
          owner: anduintransaction
          repositories: "stargazer"

      - name: Checkout
        uses: actions/checkout@v4
        with:
          repository: anduintransaction/stargazer
          ref: "${{ vars.STARGAZER_BRANCH }}"
          token: "${{ steps.generate_token.outputs.token }}"

      - uses: actions/setup-node@v4
        with:
          node-version: 21

      - name: Materialize kube config
        run: |
          mkdir -p ~/.kube
          echo "$ANDUIN_KUBE_CONFIG" | base64 -d > ~/.kube/config
        env:
          ANDUIN_KUBE_CONFIG: "${{ secrets.ANDUIN_KUBE_CONFIG }}"

      - name: Debug kubeconfig
        run: |
          kubectl config view

      - name: Migrate
        run: |
          ./ci/scripts/deploy/hotfix-migrate.sh \
            "${{ inputs.environment }}" \
            "${{ vars.STARGAZER_IMAGE }}"
        env:
          GH_NPM_READ_TOKEN: "${{ github.token }}"
          RIVENDELL_CONTEXT: "${{ vars.RIVENDELL_CONTEXT }}"
          RIVENDELL_USE_V2: "true"
          DRY_RUN: "${{ inputs.dryrun }}"
          ANDUIN_INFISICAL_CLIENT_ID: "${{ secrets.ANDUIN_INFISICAL_CLIENT_ID }}"
          ANDUIN_INFISICAL_CLIENT_SECRET: "${{ secrets.ANDUIN_INFISICAL_CLIENT_SECRET }}"
