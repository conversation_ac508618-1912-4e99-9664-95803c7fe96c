name: "[Perf] Unit/Integ test"

on:
  workflow_call:
    inputs:
      ref:
        required: true
        type: string

jobs:
  run:
    runs-on:
      - integ-runner-v2
    container:
      image: public.ecr.aws/anduintransact/anduin-runner-build:1.11.0-jammy
      volumes:
        - /var/run/secrets/kubernetes.io/serviceaccount:/var/run/secrets/kubernetes.io/serviceaccount
        - /run/docker:/run/docker
      env:
        DOCKER_HOST: unix:///run/docker/docker.sock
        KUBERNETES_SERVICE_HOST: kubernetes.default.svc
        KUBERNETES_SERVICE_PORT: "443"
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          set-safe-directory: "${{ env.GITHUB_WORKSPACE }}"
          ref: "${{ inputs.ref }}"
          fetch-depth: 0

      - name: Export namespace
        run: |
          export NAMESPACE_PREFIX="sperf-${{ inputs.ref }}"
          echo "NAMESPACE_PREFIX=$NAMESPACE_PREFIX" >> $GITHUB_ENV
          echo "NAMESPACE=$NAMESPACE_PREFIX-$(date +%s)" >> $GITHUB_ENV

      - name: Cleanup environment
        continue-on-error: true
        run: |
          ./ci/scripts/build/runner-integ-clean.sh prefix

      - name: Login Docker
        run: |
          aws ecr get-login-password --region us-east-1 | docker login --username AWS --password-stdin ************.dkr.ecr.us-east-1.amazonaws.com

      - name: Prepare environment
        timeout-minutes: 30
        run: | 
          ./ci/scripts/build/runner-integ-env.sh
        env:
          GH_NPM_READ_TOKEN: ${{ secrets.GITHUB_TOKEN }}

      - name: Debug error
        continue-on-error: true
        if: always()
        run: |
          echo "All pods"
          kubectl -n $NAMESPACE get pods

          echo "Debug failed pods"
          kubectl -n $NAMESPACE get pods | \
            grep -Ev " Running | Completed |^NAME" | \
            awk '{print $1}' | \
            xargs -I {} sh -c "kubectl -n $NAMESPACE describe pod {}; kubectl -n $NAMESPACE logs {}"

      - name: Pull cache
        run: |
          ./ci/scripts/build/runner-copy-cache.sh pull
        env:
          BRANCH_NAME: ${{ github.base_ref || github.ref_name }}

      - name: JDK info
        run: |
          java -version

      - name: Unit test
        id: unit_test
        run: |
          ./ci/scripts/build/runner-unit-test.sh
        env:
          ANDUIN_BUILD_ENV: pr

      - name: Report Unit test
        uses: mikepenz/action-junit-report@v5
        if: success() || failure()
        with:
          report_paths: ./out/**/unit-test-report.xml

      - name: Integ test
        id: integ_test
        run: |
          ./ci/scripts/build/runner-integ-test.sh
        env:
          ANDUIN_BUILD_ENV: pr
          STARGAZER_TIMEOUT: "5 minute"
        timeout-minutes: 240

      - name: Report Integ test
        uses: mikepenz/action-junit-report@v5
        if: success() || failure()
        with:
          report_paths: ./out/**/integration-test-report.xml

      - name: Report Unit test to S3
        uses: anduintransaction/gondolin/actions/branch-test-importer@master
        if: steps.unit_test.outcome == 'success' && steps.integ_test.outcome == 'success'
        with:
          report_paths: ./out/**/unit-test-report.xml
          s3_bucket: stargazer-github-actions
          s3_dir: perf/raw
          json_fn: "${{ inputs.ref }}-unit-test.json"
          branch: "${{ inputs.ref }}"
          build_info_path: ./out/platform/stargazerBuildInfo/jvm/buildInfoMembers.json

      - name: Report Integ test to S3
        uses: anduintransaction/gondolin/actions/branch-test-importer@master
        if: steps.unit_test.outcome == 'success' && steps.integ_test.outcome == 'success'
        with:
          report_paths: ./out/**/integration-test-report.xml
          s3_bucket: stargazer-github-actions
          s3_dir: perf/raw
          json_fn: "${{ inputs.ref }}-integ-test.json"
          branch: "${{ inputs.ref }}"
          build_info_path: ./out/platform/stargazerBuildInfo/jvm/buildInfoMembers.json

      - name: Cleanup environment
        if: always()
        run: |
          ./ci/scripts/build/runner-integ-clean.sh namespace
