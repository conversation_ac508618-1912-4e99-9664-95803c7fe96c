name: "[Runner] - Restart listener"

on:
  workflow_dispatch:

jobs:
  run:
    timeout-minutes: 30

    runs-on:
      - integ-runner-v2
    container:
      image: public.ecr.aws/anduintransact/anduin-runner-build:1.11.0-jammy
      volumes:
        - /var/run/secrets/kubernetes.io/serviceaccount:/var/run/secrets/kubernetes.io/serviceaccount
        - /run/docker:/run/docker
      env:
        DOCKER_HOST: unix:///run/docker/docker.sock
        KUBERNETES_SERVICE_HOST: kubernetes.default.svc
        KUBERNETES_SERVICE_PORT: "443"
 
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          set-safe-directory: "${{ env.GITHUB_WORKSPACE }}"
          fetch-depth: 0

      - name: Restart listener
        run: |
          ./scripts/restart-listener.sh
