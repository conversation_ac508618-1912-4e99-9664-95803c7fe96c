name: "[CI] Build (PR)"

on:
  workflow_call:

jobs:
  run:
    runs-on:
      - common-runner-v2
    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: ${{ github.event.pull_request.head.sha }}
          set-safe-directory: "${{ env.GITHUB_WORKSPACE }}"
          fetch-depth: 0

      - uses: actions/setup-java@v4
        with:
          distribution: "temurin"
          java-version: "21"

      - uses: anduintransaction/gondolin/actions/setup-aws-cli@master

      - name: Pull cache
        run: |
          ./ci/scripts/build/runner-copy-cache.sh pull

      - name: Build
        run: |
          ./ci/scripts/build/build.sh
        env:
          ANDUIN_BUILD_ENV: pr
