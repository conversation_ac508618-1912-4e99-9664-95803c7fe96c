name: "Build Cache"

on:
  workflow_call:
    inputs:
      ref:
        required: true
        type: string
      use-cache:
        required: false
        type: string

concurrency:
  group: "${{ github.workflow }}-${{ github.ref }}"
  cancel-in-progress: false

jobs:
  run:
    runs-on:
      - integ-runner-v2
    container:
      image: public.ecr.aws/anduintransact/anduin-runner-build:1.11.0-jammy

    steps:
      - name: Checkout
        uses: actions/checkout@v4
        with:
          ref: "${{ inputs.ref }}"
          set-safe-directory: "${{ env.GITHUB_WORKSPACE }}"
          fetch-depth: 0

      - name: JDK info
        run: |
          java -version

      - name: Pull cache
        if: ${{ inputs.use-cache == 'true' }}
        run: |
          ./ci/scripts/build/runner-copy-cache.sh pull
        env:
          BRANCH_NAME: ${{ github.base_ref || github.ref_name }}

      - name: Pull artifact
        if: ${{ inputs.use-cache == 'true' }}
        run: |
          ./ci/scripts/build/runner-copy-artifact.sh pull --remote-cache
        env:
          BRANCH_NAME: ${{ github.base_ref || github.ref_name }}

      - name: Build
        run: |
          ./ci/scripts/build/build.sh
        env:
          ANDUIN_BUILD_ENV: pr

      - name: Prepare test
        run: |
          ./ci/scripts/build/prepare-test.sh
        env:
          ANDUIN_BUILD_ENV: pr

      - name: Push cache and artifact
        run: |
          ./ci/scripts/build/runner-copy-cache.sh push
          ./ci/scripts/build/runner-copy-artifact.sh push sha-${{ inputs.ref }}
        env:
          BRANCH_NAME: ${{ github.base_ref || github.ref_name }}
