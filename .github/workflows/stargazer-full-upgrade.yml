name: "[<PERSON>ga<PERSON>] Upgrade and Migrate"

run-name: "Upgrade and Migrade ${{ inputs.environment }}"

permissions:
  contents: read
  packages: read

on:
  workflow_call:
    inputs:
      environment:
        required: true
        description: "Deploy to environment"
        type: string
  workflow_dispatch:
    inputs:
      environment:
        required: true
        description: "Deploy to environment"
        type: choice
        options:
          - "gondor-eu-canary"
          - "gondor-minas-tirith"
          - "gondor-internal"
          - "gondor-demo"
          - "gondor-public"
          - "gondor-eu-public"
          - "gondor-feature"

jobs:

  production-deployer-check:
    if: ${{ github.event_name == 'workflow_dispatch' && (inputs.environment == 'gondor-public' || inputs.environment == 'gondor-eu-public') }}
    uses: ./.github/workflows/stargazer-production-deployer-check.yml
    secrets: inherit

  upgrade:
    needs:
      - production-deployer-check
    if: ${{ always() && (needs.production-deployer-check.result == 'success' || needs.production-deployer-check.result == 'skipped') }}

    uses: ./.github/workflows/stargazer-upgrade.yml
    with:
      environment: ${{ inputs.environment }}
      dryrun: false
    secrets: inherit

  migrate:
    uses: ./.github/workflows/stargazer-migrate.yml
    with:
      environment: ${{ inputs.environment }}
      dryrun: false
    secrets: inherit
    needs:
      - upgrade
