name: "[Stargazer] Auto production upgrade"

permissions:
  contents: read
  packages: read

on:
  workflow_dispatch:
  schedule:
    - cron: "23 4 * * 2"

jobs:
  blocker-check:
    runs-on:
      - common-runner-v2
    steps:
      - name: Blocker check
        uses: anduintransaction/gondolin/actions/jira-blocker-check@master
        with:
          token: ${{ secrets.JIRA_BLOCKER_TOKEN }}
          filter_id: "10146"

      - name: Announce failure
        uses: anduintransaction/gondolin/actions/simple-slack-notification@master
        if:  ${{ failure() }}
        with:
          webhook-url: ${{ secrets.SLACK_WEBHOOK }}
          channel: release-squad
          color: danger
          title: "Jira blocker issue"
          message: "<!here> skip stargazer production deployment due to blocker issue exists. Link: <https://anduin.atlassian.net/issues/?filter=10146>"

  production-deployer-check:
    if: ${{ github.event_name == 'workflow_dispatch' }}
    uses: ./.github/workflows/stargazer-production-deployer-check.yml
    secrets: inherit

  upgrade:
    needs:
      - "blocker-check"
      - "production-deployer-check"
    if: ${{ always() && needs.blocker-check.result == 'success' && (needs.production-deployer-check.result == 'success' || needs.production-deployer-check.result == 'skipped') }}

    strategy:
      fail-fast: true
      matrix:
        environment: 
        - gondor-public
        - gondor-eu-public
    uses: ./.github/workflows/stargazer-full-upgrade.yml
    with:
      environment: ${{ matrix.environment }}
    secrets: inherit

  announce:
    needs: ["upgrade"]
    runs-on:
      - common-runner-v2
    environment: "gondor-public"
    container:
      image: public.ecr.aws/anduintransact/deployer:*******
      options: --user 1001
      volumes:
        - /var/run/secrets/kubernetes.io/serviceaccount:/var/run/secrets/kubernetes.io/serviceaccount
      env:
        KUBERNETES_SERVICE_HOST: kubernetes.default.svc
        KUBERNETES_SERVICE_PORT: "443"
    steps:
      - name: Materialize kube config
        run: |
          mkdir -p ~/.kube
          echo "$ANDUIN_KUBE_CONFIG" | base64 -d > ~/.kube/config
        env:
          ANDUIN_KUBE_CONFIG: "${{ secrets.ANDUIN_KUBE_CONFIG }}"

      - name: Debug kubeconfig
        run: |
          kubectl config view

      - name: Extract image tag
        id: image-extract
        run: |
          imageTag=$(kubectl --context ${{ vars.RIVENDELL_CONTEXT }} \
            -n gondor-public \
            get deployments gondor -o json \
            | jq -r '.spec.template.spec.containers[0].image' \
            | cut -d':' -f2)
          echo "IMAGE_TAG=${imageTag}" >> "$GITHUB_OUTPUT"

      - name: Announce
        uses: anduintransaction/gondolin/actions/simple-slack-notification@master
        with:
          webhook-url: ${{ secrets.SLACK_WEBHOOK }}
          channel: release-squad
          title: "Stargazer release announcement"
          message: "<!here> Image \\`${{ steps.image-extract.outputs.IMAGE_TAG }}\\` was released into \\`deals\\`"
