name: "Public API"

on:
  workflow_call:
    inputs:
      server:
        required: true
        type: string

jobs:
  run:
    runs-on:
      - e2e-runner
    container:
      image: public.ecr.aws/anduintransact/anduin-runner-build:1.11.0-jammy

    timeout-minutes: 150

    steps:

      - name: Checkout
        uses: actions/checkout@v4
        with:
          fetch-depth: 0

      - name: Set up JDK 17
        uses: actions/setup-java@v4
        with:
          java-version: '17'
          distribution: 'temurin'

      - name: Setup Maven
        uses: stCarolas/setup-maven@v5

      - name: Run test
        run: |
          ./ci/scripts/build/test.sh
        env:
          INPUT_SERVER: ${{ inputs.server }}
          BROWSER_STACK_KEY: ${{ secrets.BROWSER_STACK_KEY}}
          BROWSER_STACK_SECRET: ${{ secrets.BROWSER_STACK_SECRET}}

      - name: Generate report and set report summary
        if: success() || failure()
        id: report_info
        continue-on-error: true
        run: |
          ./ci/scripts/build/generate-report.sh
        env:
          INPUT_SERVER: ${{ inputs.server }}
          WORKFLOW_RUN_ID: ${{ github.run_id }}
          R2_CLOUDFLARE_KEY: ${{ secrets.R2_CLOUDFLARE_KEY }}
          R2_CLOUDFLARE_SECRET: ${{ secrets.R2_CLOUDFLARE_SECRET }}
          R2_CLOUDFLARE_URL: ${{ secrets.R2_CLOUDFLARE_URL }}

      - name: Slack notification
        if: success() || failure()
        uses: rtCamp/action-slack-notify@v2
        continue-on-error: true
        env:
          SLACK_WEBHOOK: "${{ secrets.SLACK_WEBHOOK }}"
          SLACK_ICON: "https://avatars.githubusercontent.com/u/44036562"
          SLACK_USERNAME: Actions
          SLACK_CHANNEL: public-api-test-report
          SLACK_MESSAGE: "Report: <${{ steps.report_info.outputs.REPORT_URL }}|Open>\n
          Environment: ${{ inputs.server }}\n
          ${{ steps.report_info.outputs.REPORT_SUMMARY }}\n
          ${{ steps.report_info.outputs.REPORT_APP_STATISTICS }}
          "
          SLACK_COLOR: "${{ job.status }}"
          SLACK_FOOTER: "Powered by Github Actions"
          MSG_MINIMAL: true
