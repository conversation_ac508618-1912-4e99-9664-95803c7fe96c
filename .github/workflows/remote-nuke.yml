name: "[Remote] Nuke"

run-name: "Remote nuke ${{ inputs.environment }}"

permissions:
  contents: read
  packages: read

on:
  workflow_call:
    inputs:
      stargazer-branch:
        required: true
        description: "Stargazer deploy branch"
        type: string
        default: master
      environment:
        required: true
        description: "Deploy to environment"
        type: string
        default: strider
  workflow_dispatch:
    inputs:
      stargazer-branch:
        required: true
        description: "Stargazer deploy branch"
        type: string
        default: master
      environment:
        required: true
        description: "Deploy to environment"
        type: string
        default: strider

jobs:
  nuke:
    runs-on: 
      - deployer
    environment: blackwood
    container:
      image: public.ecr.aws/anduintransact/deployer:*******
      options: --user 1001
      volumes:
        - /var/run/secrets/kubernetes.io/serviceaccount:/var/run/secrets/kubernetes.io/serviceaccount
        - /run/docker:/run/docker
      env:
        DOCKER_HOST: unix:///run/docker/docker.sock
        KUBERNETES_SERVICE_HOST: kubernetes.default.svc
        KUBERNETES_SERVICE_PORT: "443"
    steps:
      - name: Materialize kube config
        run: |
          mkdir -p ~/.kube
          echo "$ANDUIN_KUBE_CONFIG" | base64 -d > ~/.kube/config
        env:
          ANDUIN_KUBE_CONFIG: "${{ secrets.BLACKWOOD_KUBE_CONFIG }}"

      - name: Debug kubeconfig
        run: |
          kubectl config view

      - name: Run Cloudflared
        run: |
          cloudflared access tcp --hostname blackwood-sg01.anduin.fund --url 127.0.0.1:6644 &
        env:
          TUNNEL_SERVICE_TOKEN_ID: "${{ secrets.CLOUDFLARED_CLIENT_ID }}"
          TUNNEL_SERVICE_TOKEN_SECRET: "${{ secrets.CLOUDFLARED_CLIENT_SECRET }}"

      - name: Nuke old environment
        run: |
          kubectl --context ${{ vars.RIVENDELL_CONTEXT }} \
            delete ns --ignore-not-found ${{ inputs.environment }}

  upgrade:
    uses: ./.github/workflows/remote-upgrade.yml
    with:
      stargazer-branch: ${{ inputs.stargazer-branch }}
      environment: ${{ inputs.environment }}
      dryrun: false
    secrets: inherit
    needs:
      - nuke
